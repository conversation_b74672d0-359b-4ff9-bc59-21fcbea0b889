import { create } from "zustand";

interface TimelineStore {
  zoomLevel: number;
  duration: number;
  currentTime: number;
  width: number;
  previewTime: number | null; // 预览时间，用于拖拽入点/出点时的预览
  setZoomLevel: (zoomLevel: number) => void;
  setZoomLevelWithFixedPlayhead: (zoomLevel: number) => void; // 固定播放头位置的缩放
  setDuration: (duration: number) => void;
  setCurrentTime: (currentTime: number) => void;
  setWidth: (width: number) => void;
  setPreviewTime: (previewTime: number | null) => void;
  seekToTime: (time: number) => void;
  scrollToTime: (time: number) => void;
}

export const useTimelineStore = create<TimelineStore>((set, get) => ({
  zoomLevel: 1, // 默认缩放级别，在滑块中间位置附近
  duration: 60, // 默认60秒
  currentTime: 0, // 默认0秒
  width: 1000, // 默认宽度
  previewTime: null, // 默认无预览时间
  setZoomLevel: (zoomLevel) => set({ zoomLevel }),
  setZoomLevelWithFixedPlayhead: (newZoomLevel) => {
    const { currentTime, zoomLevel: oldZoomLevel } = get();

    // 获取时间轴容器元素
    const timelineContainer = document.querySelector(
      ".timeline-main-container"
    );
    if (!timelineContainer) {
      set({ zoomLevel: newZoomLevel });
      return;
    }

    // 计算播放头在当前视口中的位置
    const PIXELS_PER_SECOND = 50;
    const currentPlayheadPosition =
      currentTime * PIXELS_PER_SECOND * oldZoomLevel + 16;
    const containerScrollTop = timelineContainer.scrollTop;
    const playheadVisualPosition = currentPlayheadPosition - containerScrollTop;

    // 更新缩放级别
    set({ zoomLevel: newZoomLevel });

    // 计算新的播放头位置
    const newPlayheadPosition =
      currentTime * PIXELS_PER_SECOND * newZoomLevel + 16;

    // 计算需要的滚动位置，使播放头保持在相同的视觉位置
    const newScrollTop = newPlayheadPosition - playheadVisualPosition;

    // 延迟滚动，确保DOM已更新
    setTimeout(() => {
      timelineContainer.scrollTo({
        top: Math.max(0, newScrollTop),
        behavior: "auto", // 使用auto而不是smooth，避免视觉跳跃
      });
    }, 0);
  },
  setDuration: (duration) => set({ duration }),
  setCurrentTime: (currentTime) => set({ currentTime }),
  setWidth: (width) => set({ width }),
  setPreviewTime: (previewTime) => set({ previewTime }),
  seekToTime: (time) => {
    const { duration } = get();
    const clampedTime = Math.max(0, Math.min(duration, time));
    set({ currentTime: clampedTime });
  },
  scrollToTime: (time) => {
    const { duration, zoomLevel } = get();
    const clampedTime = Math.max(0, Math.min(duration, time));

    // 计算滚动位置
    const PIXELS_PER_SECOND = 50; // 与时间轴组件中的常量保持一致
    const TIMELINE_PADDING_TOP = 16; // py-4 = 16px 顶部内边距
    const scrollPosition =
      clampedTime * PIXELS_PER_SECOND * zoomLevel + TIMELINE_PADDING_TOP;

    // 查找时间轴容器并滚动
    const timelineContainer = document.querySelector(
      ".timeline-main-container"
    );
    if (timelineContainer) {
      // 获取容器高度，计算居中偏移
      const containerHeight = timelineContainer.clientHeight;
      const centerOffset = containerHeight / 2;

      timelineContainer.scrollTo({
        top: Math.max(0, scrollPosition - centerOffset), // 居中显示，但不小于0
        behavior: "smooth",
      });
    }
  },
}));
