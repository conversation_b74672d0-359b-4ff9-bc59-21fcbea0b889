import { create } from "zustand";

interface TimelineStore {
  zoomLevel: number;
  duration: number;
  currentTime: number;
  width: number;
  setZoomLevel: (zoomLevel: number) => void;
  setDuration: (duration: number) => void;
  setCurrentTime: (currentTime: number) => void;
  setWidth: (width: number) => void;
  seekToTime: (time: number) => void;
  scrollToTime: (time: number) => void;
}

export const useTimelineStore = create<TimelineStore>((set, get) => ({
  zoomLevel: 1, // 默认缩放级别，在滑块中间位置附近
  duration: 60, // 默认60秒
  currentTime: 0, // 默认0秒
  width: 1000, // 默认宽度
  setZoomLevel: (zoomLevel) => set({ zoomLevel }),
  setDuration: (duration) => set({ duration }),
  setCurrentTime: (currentTime) => set({ currentTime }),
  setWidth: (width) => set({ width }),
  seekToTime: (time) => {
    const { duration } = get();
    const clampedTime = Math.max(0, Math.min(duration, time));
    set({ currentTime: clampedTime });
  },
  scrollToTime: (time) => {
    const { duration, zoomLevel } = get();
    const clampedTime = Math.max(0, Math.min(duration, time));
    
    // 计算滚动位置
    const PIXELS_PER_SECOND = 50; // 与时间轴组件中的常量保持一致
    const TIMELINE_PADDING_TOP = 16; // py-4 = 16px 顶部内边距
    const scrollPosition = clampedTime * PIXELS_PER_SECOND * zoomLevel + TIMELINE_PADDING_TOP;
    
    // 查找时间轴容器并滚动
    const timelineContainer = document.querySelector('.timeline-main-container');
    if (timelineContainer) {
      // 获取容器高度，计算居中偏移
      const containerHeight = timelineContainer.clientHeight;
      const centerOffset = containerHeight / 2;
      
      timelineContainer.scrollTo({
        top: Math.max(0, scrollPosition - centerOffset), // 居中显示，但不小于0
        behavior: 'smooth'
      });
    }
  },
}));
